# 设置输出编码为 UTF-8
$OutputEncoding = [System.Text.Encoding]::UTF8
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$env:PYTHONIOENCODING = "utf-8"
$env:LANG = "en_US.UTF-8"
$ErrorActionPreference = "Stop"

# 阿里云镜像仓库配置
$registry = "crpi-dzk2aw0old0xwqw9.cn-shanghai.personal.cr.aliyuncs.com"

# 提示用户选择 tag 后缀
Write-Host "`n🔥 请选择镜像后缀（只影响 tag 名）：" -ForegroundColor Yellow
Write-Host "1️⃣ frontend"
Write-Host "2️⃣ fab"
$choice = Read-Host "请输入选项编号 (1 或 2)"

if ($choice -eq "1") {
    $tagSuffix = "frontend"
} elseif ($choice -eq "2") {
    $tagSuffix = "fab"
} else {
    Write-Host "❌ 无效的选项！脚本终止。" -ForegroundColor Red
    Read-Host "👀 请重新运行脚本并选择正确的选项，按回车关闭窗口"
    exit 1
}

# 定义要复制的目录列表
$copyTasks = @(
    @{ source = "D:\公司代码\cube-web\build"; target = "D:\公司代码\cube-web\static\appbuilder\frontend" },
    @{ source = "D:\公司代码\cube-pipeline\build"; target = "D:\公司代码\cube-web\static\appbuilder\vison" }
)

function Copy-Files($sourcePath, $targetPath) {
    Write-Host "`n📁 正在复制 $sourcePath 到 $targetPath ..." -ForegroundColor Cyan
    if (Test-Path $sourcePath) {
        if (-Not (Test-Path $targetPath)) {
            New-Item -ItemType Directory -Path $targetPath -Force | Out-Null
        }
        Copy-Item -Path "$sourcePath\*" -Destination $targetPath -Recurse -Force
        Write-Host "✅ 复制完成: $targetPath" -ForegroundColor Green
    } else {
        Write-Host "❌ 源目录不存在：$sourcePath" -ForegroundColor Red
        Read-Host "👀 请检查路径是否正确，按回车关闭窗口"
        exit 1
    }
}

# 复制所有目录
foreach ($task in $copyTasks) {
    Copy-Files $task.source $task.target
}

# 构建镜像
$tag = Get-Date -Format "yyyyMMdd_HHmm"
$localImageName = "10to3/kubeflow-dashboard-frontend:$tag-$tagSuffix"
$registryImageName = "$registry/10to3/kubeflow-dashboard-frontend:$tag-$tagSuffix"

Write-Host "`n🌟 正在构建镜像: $localImageName" -ForegroundColor Cyan
docker build --network=host -t $localImageName -f docker/Dockerfile .

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ 构建失败！镜像未生成，终止上传流程。" -ForegroundColor Red
    Read-Host "👀 请检查 Dockerfile 或网络问题后重试，按回车关闭窗口"
    exit 1
}

# 标记镜像
Write-Host "`n🏷️  正在标记镜像..." -ForegroundColor Yellow
docker tag $localImageName $registryImageName

# 推送镜像到阿里云
Write-Host "`n⬆️  正在推送镜像到阿里云容器镜像服务..." -ForegroundColor Yellow
docker push $registryImageName

if ($LASTEXITCODE -ne 0) {
    Write-Host "`n❌ 推送失败！请检查网络连接或权限设置。" -ForegroundColor Red
    Read-Host "👀 请检查后重试，按回车关闭窗口"
    exit 1
}

Write-Host "`n✅ 推送完成！镜像地址为: $registryImageName" -ForegroundColor Green

# 生成部署命令
$cmd = @"
docker rm -f `$(docker ps -aq --filter name=kubeflow-dashboard-frontend) 2>/dev/null
docker images --format "{{.ID}}" $registry/10to3/kubeflow-dashboard-frontend | xargs docker rmi -f
docker pull $registryImageName
docker run -d --name kubeflow-dashboard-frontend -p 8978:8000 $registryImageName
"@

Write-Host "`n请复制以下命令到 Xshell 执行：" -ForegroundColor Cyan
Write-Host "`n$cmd" -ForegroundColor Green

Read-Host "`n✨ 一切正常，按回车关闭窗口"
