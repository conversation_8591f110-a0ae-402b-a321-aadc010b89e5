user nginx;
worker_processes auto;
worker_rlimit_nofile 65535;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 声明环境变量
env ADMIN_SERVER;

events {
    worker_connections 10240;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    access_log /var/log/nginx/access.log main;

    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    client_max_body_size 20M;
    client_body_buffer_size 128k;

    # 开启 Gzip
    gzip on;
    gzip_disable "msie6";
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain text/css text/xml text/javascript application/javascript application/x-javascript
        application/json application/xml application/xml+rss application/vnd.ms-fontobject
        application/x-font-ttf font/opentype image/svg+xml image/x-icon;

    client_header_buffer_size 128k;        # 增加默认请求头缓冲区大小
    large_client_header_buffers 8 256k;    # 增加大型请求头缓冲区大小和数量
    proxy_buffer_size 16k;                 # 代理缓冲区大小
    proxy_buffers 4 32k;
    proxy_busy_buffers_size 64k;

    server {
        listen 8000;
        server_name localhost;

        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-XSS-Protection "1; mode=block";
        add_header X-Content-Type-Options nosniff;

        # 静态资源/前端项目
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri /index.html;
        }


        location /api/ {
          proxy_pass http://$ADMIN_SERVER/api/;

          proxy_http_version 1.1;
          proxy_set_header Connection '';
          chunked_transfer_encoding on;

          proxy_buffering off;
          proxy_cache off;

          proxy_set_header Host $host;
          proxy_set_header X-Real-IP $remote_addr;
          proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
          proxy_set_header X-Forwarded-Proto $scheme;
        }


        # 健康检查
        location /health {
            access_log off;
            return 200 'healthy\n';
        }

        # 错误页
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }
}
